import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON> } from 'vscode';
import { ProgressLocation } from 'vscode';
import type { Container } from '../container';
import type { MarkdownContentMetadata } from '../documents/markdown';
import { uncommitted, uncommittedStaged } from '../git/models/revision';
import { showGenericErrorMessage } from '../messages';
import { command } from '../system/-webview/command';
import { showMarkdownPreview } from '../system/-webview/markdown';
import { createMarkdownCommandLink } from '../system/commands';
import { Logger } from '../system/logger';
import { capitalize } from '../system/string';
import type { CommandContext } from './commandContext';
import {
	isCommandContextViewNodeHasRepoPath,
	isCommandContextViewNodeHasRepository,
	isCommandContextViewNodeHasWorktree,
} from './commandContext.utils';
import type { ExplainBaseArgs } from './explainBase';
import { ExplainCommandBase } from './explainBase';

export interface ExplainWipCommandArgs extends ExplainBaseArgs {
	staged?: boolean;
}

@command()
export class ExplainWipCommand extends ExplainCommandBase {
	static createMarkdownCommandLink(args: ExplainWipCommandArgs): string {
		return createMarkdownCommandLink<ExplainWipCommandArgs>('gitlens.ai.explainWip:editor', args);
	}

	pickerTitle = 'Explain Working Changes';
	repoPickerPlaceholder = 'Choose which repository to explain working changes from';

	constructor(container: Container) {
		super(container, ['gitlens.ai.explainWip', 'gitlens.ai.explainWip:editor', 'gitlens.ai.explainWip:views']);
	}

	protected override preExecute(context: CommandContext, args?: ExplainWipCommandArgs): Promise<void> {
		if (isCommandContextViewNodeHasWorktree(context)) {
			args = { ...args };
			args.repoPath = context.node.worktree.repoPath;
			args.worktreePath = context.node.worktree.path;
			args.source = args.source ?? { source: 'view', type: 'wip' };
		} else if (isCommandContextViewNodeHasRepository(context)) {
			args = { ...args };
			args.repoPath = context.node.repo.path;
			args.source = args.source ?? { source: 'view', type: 'wip' };
		} else if (isCommandContextViewNodeHasRepoPath(context)) {
			args = { ...args };
			args.repoPath = context.node.repoPath;
			args.source = args.source ?? { source: 'view', type: 'wip' };
		}

		return this.execute(context.editor, context.uri, args);
	}

	async execute(editor?: TextEditor, uri?: Uri, args?: ExplainWipCommandArgs): Promise<void> {
		args = { ...args };

		// Get the diff of working changes
		const svc = await this.getRepositoryService(editor, uri, args);
		if (svc?.diff?.getDiff == null) {
			void showGenericErrorMessage('Unable to get diff service');
			return;
		}

		args.repoPath ??= svc.path;

		try {
			let label;
			let to;
			if (args?.staged === true) {
				label = 'staged';
				to = uncommittedStaged;
			} else if (args?.staged === false) {
				label = 'unstaged';
				to = uncommitted;
			} else {
				label = 'working';
				to = '';
			}

			const diff = await svc.diff.getDiff(to, undefined);
			if (!diff?.contents) {
				void showGenericErrorMessage(`No ${label} changes found to explain`);
				return;
			}

			// Get worktree info
			let worktreeInfo = '';
			let worktreeDisplayName = '';

			if (args?.worktreePath) {
				// Get the worktree name if available
				const worktrees = await svc.worktrees?.getWorktrees();
				const worktree = worktrees?.find(w => w.path === args.worktreePath);

				if (worktree) {
					worktreeInfo = ` in ${worktree.name}`;
					worktreeDisplayName = ` (${worktree.name})`;
				} else {
					worktreeInfo = ` in worktree`;
					worktreeDisplayName = ` (${args.worktreePath.toString()})`;
				}
			}

			// Call the AI service to explain the changes
			const result = await this.container.ai.explainChanges(
				{
					diff: diff.contents,
					message: `${capitalize(label)} changes${worktreeInfo}`,
				},
				{
					...args.source,
					source: args.source?.source ?? 'commandPalette',
					type: 'wip',
				},
				{
					progress: {
						location: ProgressLocation.Notification,
						title: `Explaining ${label} changes${worktreeInfo}...`,
					},
				},
			);

			if (result == null) {
				void showGenericErrorMessage(`Unable to explain ${label} changes`);
				return;
			}

			const title = `${capitalize(label)} Changes Summary${worktreeDisplayName}`;
			const subtitle = `${capitalize(label)} Changes`;
			const content = `# ${title}\n\n> Generated by ${result.model.name}\n\n## ${subtitle}\n\n${result.parsed.summary}\n\n${result.parsed.body}`;

			const contentMetadata = {
				header: {
					title: title,
					subtitle: subtitle,
					aiModel: result.model.name,
				},
				command: {
					label: 'Explain Working Changes',
					name: 'gitlens.ai.explainWip',
					args: args,
				},
			};

			const documentUri = this.container.markdown.openDocument(
				content,
				`/explain/wip/${result.model.id}`,
				subtitle,
				contentMetadata as MarkdownContentMetadata,
			);

			showMarkdownPreview(documentUri);
		} catch (ex) {
			Logger.error(ex, 'ExplainWipCommand', 'execute');
			void showGenericErrorMessage('Unable to explain working changes');
		}
	}
}
