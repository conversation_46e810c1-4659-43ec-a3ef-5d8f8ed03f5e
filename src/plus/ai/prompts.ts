import type { PromptTemplate } from './models/promptTemplates';

export const generateCommitMessage: PromptTemplate<'generate-commitMessage'> = {
	id: 'generate-commitMessage_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise but meaningful commit message. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative commit message that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective commit message, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the commit message:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the summary of your commit message
   - Start with a third-person singular present tense verb
   - Limit to 50 characters if possible
4. If necessary, provide a brief explanation of the changes, which will be the body of your commit message
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes
5. If the changes are related to a specific issue or ticket, include the reference (e.g., "Fixes #123" or "Relates to JIRA-456") at the end of the commit message

Write your commit message summary inside <summary> tags and your commit message body inside <body> tags and include no other text
Example output structure:
<summary>
[commit-message-summary-goes-here]
</summary>
<body>
[commit-message-body-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful commit message following the instructions above`,
};

export const generateCreatePullRequest: PromptTemplate<'generate-create-pullRequest'> = {
	id: 'generate-create-pullRequest_v2',
	variables: ['diff', 'data', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise but meaningful pull request title and description. You will be provided with a code diff and a list of commits. Your goal is to analyze the changes and create a clear, informative title and description that accurately represents the modifications made to the code
First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Then, review the list of commits to help understand the motivation behind the changes and any relevant background information:
<~~data~~>
\${data}
</~~data~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective pull request title and description, follow these steps:

1. Carefully analyze the diff, commit messages, context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the pull request title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title of your pull request
   - Start with a third-person singular present tense verb
   - Limit to 50 characters if possible
4. Provide a detailed explanation of the changes, which will be the body of your pull request
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes
   - Structure the body with markdown bullets and headings for clarity
5. If the changes are related to a specific issue or ticket, include the reference (e.g., "Fixes #123" or "Relates to JIRA-456") at the end of the pull request message

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[pull-request-title-goes-here]
</summary>
<body>
[pull-request-body-goes-here]
</body>

\${instructions}

Based on the provided code diff, commit list, and any additional context, create a concise but meaningful pull request title and body following the instructions above`,
};

export const generateStashMessage: PromptTemplate<'generate-stashMessage'> = {
	id: 'generate-stashMessage_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with creating a concise but descriptive stash message. You will be provided with a code diff of uncommitted changes. Your goal is to analyze the changes and create a clear, single-line stash message that accurately represents the work in progress being stashed

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

To create an effective stash message, follow these steps:

1. Analyze the changes and focus on:
   - The primary feature or bug fix was being worked on
   - The overall intent of the changes
   - Any notable file or areas being modified
2. Create a single-line message that:
   - Briefly describes the changes being stashed but must be descriptive enough to identify later
   - Prioritizes the most significant change if multiple changes are present. If multiple related changes are significant, try to summarize them concisely
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')

Write your stash message inside <summary> tags and include no other text
Example output structure:
<summary>
[stash-message-goes-here]
</summary>

\${instructions}

Based on the provided code diff, create a concise but descriptive stash message following the instructions above`,
};

export const generateCreateCloudPatch: PromptTemplate<'generate-create-cloudPatch'> = {
	id: 'generate-create-cloudPatch_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise and meaningful title and description. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative title and description that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective title and description, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title
4. Provide a detailed explanation of the changes, which will be the description
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[cloud-patch-title-goes-here]
</summary>
<body>
[cloud-patch-description-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful title and description following the instructions above`,
};

export const generateCreateCodeSuggest: PromptTemplate<'generate-create-codeSuggestion'> = {
	id: 'generate-create-codeSuggestion_v2',
	variables: ['diff', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with summarizing code changes into a concise and meaningful code review title and description. You will be provided with a code diff and optional additional context. Your goal is to analyze the changes and create a clear, informative code review title and description that accurately represents the modifications made to the code

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, if provided, use this context to understand the motivation behind the changes and any relevant background information:
<~~additional-context~~>
\${context}
</~~additional-context~~>

To create an effective title and description, follow these steps:

1. Carefully analyze the diff and context, focusing on:
   - The purpose and rationale of the changes
   - Any problems addressed or benefits introduced
   - Any significant logic changes or algorithmic improvements
2. Ensure the following when composing the title and description:
   - Emphasize the 'why' of the change, its benefits, or the problem it addresses
   - Use an informal yet professional tone
   - Use a future-oriented manner, third-person singular present tense (e.g., 'Fixes', 'Updates', 'Improves', 'Adds', 'Removes')
   - Be clear and concise
   - Synthesize only meaningful information from the diff and context
   - Avoid outputting code, specific code identifiers, names, or file names unless crucial for understanding
   - Avoid repeating information, broad generalities, and unnecessary phrases like "this", "this commit", or "this change"
3. Summarize the main purpose of the changes in a single, concise sentence, which will be the title
4. Provide a detailed explanation of the changes, which will be the description
   - Add line breaks for readability and to separate independent ideas
   - Focus on the "why" rather than the "what" of the changes

Write your title inside <summary> tags and your description inside <body> tags and include no other text
Example output structure:
<summary>
[code-suggestion-title-goes-here]
</summary>
<body>
[code-suggestion-description-goes-here]
</body>

\${instructions}

Based on the provided code diff and any additional context, create a concise but meaningful code review title and description following the instructions above`,
};

export const explainChanges: PromptTemplate<'explain-changes'> = {
	id: 'explain-changes',
	variables: ['diff', 'message', 'instructions'],
	template: `You are an advanced AI programming assistant and are tasked with creating clear, technical summaries of code changes that help reviewers understand the modifications and their implications. You will analyze a code diff and the author-provided message to produce a structured summary that captures the essential aspects of the changes

First, examine the following code changes provided in Git diff format:
<~~diff~~>
\${diff}
</~~diff~~>

Now, review the author-provided message to help understand the motivation behind the changes and any relevant background information:
<~~message~~>
\${message}
</~~message~~>

Analysis Instructions:
1. Examine the technical changes and their direct implications
2. Consider the scope of changes (small fix vs. major modification)
3. Identify any structural or behavioral changes
4. Look for potential side effects or dependencies
5. Note any obvious testing implications

Write your summary inside <summary> and <body> tags in the following structured markdown format, text in [] brackets should be replaced with your own text, if applicable, not including the brackets:
<summary>
[Concise, one-line description of the change]

[2-3 sentences explaining the core changes and their purpose]
</summary>
<body>
### Changes
- [Key technical modifications]
- [Important structural changes]
- [Modified components/files]

### Impact
- [Behavioral changes]
- [Dependencies affected]
- [Breaking changes, if any]
- [Performance implications, if apparent]
</body>

Guidelines:
- Keep the initial description under 80 characters
- Use clear, technical language
- Focus on observable changes from the diff
- Highlight significant code structure changes
- Base conclusions only on the code diff and message
- Avoid assumptions about business context
- Include specific file/component names only when relevant

\${instructions}

Based on the provided code diff and message, create a focused technical summary following the format above`,
};

export const generateChangelog: PromptTemplate<'generate-changelog'> = {
	id: 'generate-changelog',
	variables: ['data', 'instructions'],
	template: `You are an expert at creating changelogs in the "Keep a Changelog" format (https://keepachangelog.com). Your task is to create a set of clear, informative changelog entries

First, carefully examine the following JSON data containing commit messages and associated issues. The data is structured as an array of "change" objects. Each "change" contains a \`message\` (the commit message) and an \`issues\` array. The \`issues\` array contains objects representing associated issues, each with an \`id\`, \`url\`, and optional \`title\`

<~~data~~>
\${data}
</~~data~~>

Guidelines for creating the changelog:

1. Analyze the commit messages and associated issue titles (if available) to understand the changes made. Be sure to read every commit message and associated issue titles to understand the purpose of each change
2. Group changes into these categories (only include categories with actual changes):
   - Added: New features or capabilities
   - Changed: Changes to existing functionality
   - Deprecated: Features that will be removed in upcoming releases
   - Removed: Features that were removed
   - Fixed: Bug fixes
   - Security: Security-related changes
3. Order entries by importance within each category
4. Write a clear, concise, user-friendly descriptions for each change that focuses on the impact to users
   - Follow the example structure below of the Keep a Changelog format for each entry
   - Start with a third-person singular present tense verb (e.g., "Adds", "Changes", "Improves", "Removes", "Deprecates", "Fixes", etc.)
   - Avoid technical implementation details unless directly relevant to users
   - Combine related changes into single entries when appropriate, grouping the associated issues together as well
   - Focus on the what and why, not the how. One sentence is often sufficient, though bullets can be used for multiple related points
5. Prioritize user-facing changes. If a commit message describes internal refactoring or implementation details, try to infer the user-facing impact (if any) from the issue titles or other commits. If there's no user-facing impact, and no clear external benefit, omit the change
6. Use Markdown headings, links, and bullet points, adhering to Keep a Changelog structure
7. Provide only the changelog entry—no additional text or commentary outside of the changelog

Example output structure:

### Added
- Adds brief description of the added feature ([#Issue-ID](Issue-URL))

### Changed
- Changes brief description of how something changed ([#Issue-ID](Issue-URL))
- Improves brief description of how something improved ([#Issue-ID](Issue-URL))

### Fixed
- Fixes Issue Title or brief description if no title ([#Issue-ID](Issue-URL))

\${instructions}

Based on the provided commit messages and associated issues, create a set of markdown changelog entries following the instructions above. Do not include any explanatory text or metadata`,
};

export const generateRebase: PromptTemplate<'generate-rebase'> = {
	id: 'generate-rebase',
	variables: ['diff', 'commits', 'data', 'context', 'instructions'],
	template: `You are an advanced AI programming assistant tasked with organizing code changes into commits. Your goal is to create a new set of commits that are related, grouped logically, atomic, and easy to review. You will be working with code changes provided in a unified diff format.

First, examine the following unified Git diff of code changes:

<unified_diff>
\${diff}
</unified_diff>

Next, examine the following JSON array which represents a mapping of index to hunk headers in the unified_diff to be used later when mapping hunks to commits:
<hunk_map>
\${data}
</hunk_map>


Your task is to group the hunks in unified_diff into a set of commits, ordered into a commit history as an array. Follow these guidelines:

1. Only organize the hunks themselves, not individual lines within hunks.
2. Group hunks into logical units that make sense together and can be applied atomically.
3. Ensure each commit is self-contained and only depends on commits that come before it in the new history.
4. Write meaningful commit messages that accurately describe the changes in each commit.
5. Provide a detailed explanation of the changes in each commit.
6. Make sure the new commit history is easy to review and understand.

Output your new commit history as a JSON array. Each commit in the array should be an object representing a grouping of hunks forming that commit, with the following properties:
- "message": A string containing the commit message.
- "explanation": A string with a detailed explanation of the changes in the commit. Write the explanation as if you were explaining the changes to a reviewer who is familiar with the codebase but not the specific changes. Walk through the changes and make references to specific changes where needed, explaining how they achieve the objective of the commit.
- "hunks": An array of objects, each representing a hunk in the commit. Each hunk object should have:
  - "hunk": The hunk index (number) from the hunk_map, matching the equivalent hunk you chose from the unified_diff.

Once you've completed your analysis, generate the JSON output following the specified format.

Here's an example of the expected JSON structure (note that this is just a structural example and does not reflect the actual content you should produce):

[
  {
    "message": "Example commit message",
    "explanation": "Detailed explanation of the changes in this commit",
    "hunks": [
      {
        "hunk": 2
      },
      {
        "hunk": 7
      }
    ]
  }
]

Remember to base your organization of commits solely on the provided unified_diff and hunk_map. Do not introduce any new changes or modify the content of the hunks. Your task is to organize the existing hunks in a logical and reviewable manner.

\${instructions}

Now, proceed with your analysis and organization of the commits. Output only the JSON array containing the commits, and nothing else.`,
};
